#!/usr/bin/env node

/**
 * Arien-AI CLI Terminal System
 * A modern CLI terminal with LLM function tools calling capabilities
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { readFileSync } from 'fs';
import { Command } from 'commander';
import chalk from 'chalk';
import updateNotifier from 'update-notifier';

// ES2025 features
import { AgentEngine } from './core/agent-engine.js';
import { TerminalUI } from './cli/terminal-ui.js';
import { Config } from './utils/config.js';
// Logger will be imported dynamically after config is loaded

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Initialize package info for update notifications
const pkg = JSON.parse(readFileSync(join(__dirname, '../package.json'), 'utf8'));
updateNotifier({ pkg }).notify();

class ArienAI {
  #config;
  #logger;
  #agentEngine;
  #terminalUI;
  #isInitialized = false;

  constructor() {
    this.#config = new Config();
    this.#logger = null; // Will be initialized after config is loaded
  }

  async initialize() {
    if (this.#isInitialized) return;

    try {
      // Load configuration first
      await this.#config.load();

      // Initialize logger after config is loaded
      const { Logger } = await import('./utils/logger.js');
      this.#logger = new Logger(this.#config);

      this.#logger.info('🚀 Initializing Arien-AI...');

      // Initialize core components
      this.#agentEngine = new AgentEngine(this.#config, this.#logger);
      this.#terminalUI = new TerminalUI(this.#config, this.#logger);

      // Initialize agent engine
      await this.#agentEngine.initialize();

      this.#isInitialized = true;
      this.#logger.info('✅ Arien-AI initialized successfully');

    } catch (error) {
      console.error('❌ Failed to initialize Arien-AI:', error);
      process.exit(1);
    }
  }

  async startInteractiveMode() {
    await this.initialize();
    
    console.log(chalk.cyan.bold(`
╔══════════════════════════════════════════════════════════════╗
║                        🤖 Arien-AI                          ║
║              Intelligent CLI Terminal Assistant             ║
║                                                              ║
║  Type 'help' for commands, 'exit' to quit                   ║
║  Use natural language to describe what you want to do       ║
╚══════════════════════════════════════════════════════════════╝
    `));

    await this.#terminalUI.startInteractiveSession(this.#agentEngine);
  }

  async executeCommand(command, options = {}) {
    await this.initialize();
    
    try {
      const result = await this.#agentEngine.processUserInput(command, {
        interactive: false,
        ...options
      });
      
      if (result.success) {
        console.log(chalk.green('✅ Command executed successfully'));
        if (result.output) {
          console.log(result.output);
        }
      } else {
        console.log(chalk.red('❌ Command failed'));
        if (result.error) {
          console.error(result.error);
        }
        process.exit(1);
      }
      
    } catch (error) {
      this.#logger.error('Command execution failed:', error);
      console.error(chalk.red('❌ An unexpected error occurred'));
      process.exit(1);
    }
  }

  async showStatus() {
    await this.initialize();
    
    const status = await this.#agentEngine.getStatus();
    
    console.log(chalk.blue.bold('\n📊 Arien-AI Status\n'));
    console.log(chalk.white(`Version: ${chalk.green(pkg.version)}`));
    console.log(chalk.white(`Node.js: ${chalk.green(process.version)}`));
    console.log(chalk.white(`Platform: ${chalk.green(process.platform)}`));
    console.log(chalk.white(`Architecture: ${chalk.green(process.arch)}`));
    
    console.log(chalk.blue.bold('\n🔧 Configuration\n'));
    console.log(chalk.white(`Config File: ${chalk.green(status.configPath)}`));
    console.log(chalk.white(`Log Level: ${chalk.green(status.logLevel)}`));
    
    console.log(chalk.blue.bold('\n🤖 LLM Providers\n'));
    for (const [name, provider] of Object.entries(status.providers)) {
      const statusIcon = provider.available ? '🟢' : '🔴';
      console.log(chalk.white(`${statusIcon} ${name}: ${provider.status}`));
    }
    
    console.log(chalk.blue.bold('\n🛠️  Available Tools\n'));
    for (const tool of status.tools) {
      console.log(chalk.white(`• ${tool.name}: ${tool.description}`));
    }
  }
}

// CLI Command Setup
const program = new Command();

program
  .name('arien')
  .description('Arien-AI: Intelligent CLI Terminal Assistant')
  .version(pkg.version);

program
  .command('interactive')
  .alias('i')
  .description('Start interactive mode')
  .action(async () => {
    const arien = new ArienAI();
    await arien.startInteractiveMode();
  });

program
  .command('execute <command>')
  .alias('exec')
  .description('Execute a single command')
  .option('-v, --verbose', 'Verbose output')
  .option('--no-retry', 'Disable retry logic')
  .action(async (command, options) => {
    const arien = new ArienAI();
    await arien.executeCommand(command, options);
  });

program
  .command('status')
  .description('Show system status and configuration')
  .action(async () => {
    const arien = new ArienAI();
    await arien.showStatus();
  });

program
  .command('config')
  .description('Manage configuration')
  .option('--init', 'Initialize configuration')
  .option('--reset', 'Reset to default configuration')
  .action(async (options) => {
    const config = new Config();
    
    if (options.init) {
      await config.initialize();
      console.log(chalk.green('✅ Configuration initialized'));
    } else if (options.reset) {
      await config.reset();
      console.log(chalk.green('✅ Configuration reset to defaults'));
    } else {
      await config.show();
    }
  });

// Default action - start interactive mode
program.action(async () => {
  const arien = new ArienAI();
  await arien.startInteractiveMode();
});

// Error handling
process.on('uncaughtException', (error) => {
  console.error(chalk.red('💥 Uncaught Exception:'), error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('💥 Unhandled Rejection at:'), promise, 'reason:', reason);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log(chalk.yellow('\n👋 Goodbye! Arien-AI shutting down gracefully...'));
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log(chalk.yellow('\n👋 Goodbye! Arien-AI shutting down gracefully...'));
  process.exit(0);
});

// Parse command line arguments
program.parse();
