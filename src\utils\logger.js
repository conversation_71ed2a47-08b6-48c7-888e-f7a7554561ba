/**
 * Advanced Logging System
 * Provides structured logging with multiple outputs and log rotation
 */

import { writeFileSync, appendFileSync, existsSync, mkdirSync, statSync, unlinkSync, readdirSync } from 'fs';
import { join, dirname } from 'path';
import { homedir } from 'os';
import chalk from 'chalk';

export class Logger {
  #config;
  #logLevels = {
    error: 0,
    warn: 1,
    info: 2,
    debug: 3
  };
  #colors = {
    error: chalk.red,
    warn: chalk.yellow,
    info: chalk.blue,
    debug: chalk.gray
  };

  constructor(config) {
    this.#config = config;
    this.#ensureLogDirectory();
  }

  #getLogFilePath() {
    const logConfig = this.#config.getLoggingConfig();
    let logFile = logConfig.file;

    // Expand tilde to home directory
    if (logFile.startsWith('~')) {
      logFile = logFile.replace('~', homedir());
    }

    return logFile;
  }

  #ensureLogDirectory() {
    const logFile = this.#getLogFilePath();
    const logDir = dirname(logFile);

    if (!existsSync(logDir)) {
      mkdirSync(logDir, { recursive: true });
    }
  }

  #shouldLog(level) {
    const logConfig = this.#config.getLoggingConfig();
    const currentLevel = this.#logLevels[logConfig.level] ?? 2;
    const messageLevel = this.#logLevels[level] ?? 2;
    return messageLevel <= currentLevel;
  }

  #formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const formattedMeta = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
    
    return {
      console: `[${timestamp}] ${level.toUpperCase()}: ${message}${formattedMeta}`,
      file: JSON.stringify({
        timestamp,
        level: level.toUpperCase(),
        message,
        meta,
        pid: process.pid
      })
    };
  }

  #writeToFile(content) {
    try {
      const logFile = this.#getLogFilePath();

      // Check if log rotation is needed
      this.#rotateLogsIfNeeded();

      appendFileSync(logFile, content + '\n');
    } catch (error) {
      console.error(chalk.red('Failed to write to log file:'), error.message);
    }
  }

  #rotateLogsIfNeeded() {
    const logConfig = this.#config.getLoggingConfig();
    
    if (!existsSync(logConfig.file)) return;
    
    const stats = statSync(logConfig.file);
    if (stats.size >= logConfig.maxSize) {
      this.#rotateLogs();
    }
  }

  #rotateLogs() {
    const logConfig = this.#config.getLoggingConfig();
    const logDir = dirname(logConfig.file);
    const logName = 'arien-ai.log';
    
    try {
      // Remove oldest log if we have too many
      const logFiles = readdirSync(logDir)
        .filter(file => file.startsWith('arien-ai.log'))
        .sort()
        .reverse();
      
      if (logFiles.length >= logConfig.maxFiles) {
        const oldestLog = logFiles[logFiles.length - 1];
        unlinkSync(join(logDir, oldestLog));
      }
      
      // Rotate current log
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const rotatedName = `arien-ai.log.${timestamp}`;
      
      const currentContent = existsSync(logConfig.file) ? 
        require('fs').readFileSync(logConfig.file, 'utf8') : '';
      
      if (currentContent) {
        writeFileSync(join(logDir, rotatedName), currentContent);
        writeFileSync(logConfig.file, ''); // Clear current log
      }
      
    } catch (error) {
      console.error(chalk.red('Failed to rotate logs:'), error.message);
    }
  }

  #log(level, message, meta = {}) {
    if (!this.#shouldLog(level)) return;
    
    const formatted = this.#formatMessage(level, message, meta);
    const logConfig = this.#config.getLoggingConfig();
    
    // Console output
    if (logConfig.console) {
      const colorFn = this.#colors[level] || chalk.white;
      console.log(colorFn(formatted.console));
    }
    
    // File output
    this.#writeToFile(formatted.file);
  }

  error(message, meta = {}) {
    this.#log('error', message, meta);
  }

  warn(message, meta = {}) {
    this.#log('warn', message, meta);
  }

  info(message, meta = {}) {
    this.#log('info', message, meta);
  }

  debug(message, meta = {}) {
    this.#log('debug', message, meta);
  }

  // Specialized logging methods
  logCommand(command, result) {
    this.info('Command executed', {
      command,
      success: result.success,
      exitCode: result.exitCode,
      duration: result.duration
    });
  }

  logLLMRequest(provider, model, prompt, response) {
    this.debug('LLM request', {
      provider,
      model,
      promptLength: prompt.length,
      responseLength: response?.length || 0,
      timestamp: Date.now()
    });
  }

  logError(error, context = {}) {
    this.error(error.message, {
      stack: error.stack,
      name: error.name,
      context
    });
  }

  logPerformance(operation, duration, meta = {}) {
    this.info(`Performance: ${operation}`, {
      duration: `${duration}ms`,
      ...meta
    });
  }

  logRetry(attempt, maxAttempts, error, delay) {
    this.warn(`Retry attempt ${attempt}/${maxAttempts}`, {
      error: error.message,
      nextDelay: `${delay}ms`
    });
  }

  logToolExecution(toolName, input, output, duration) {
    this.info(`Tool executed: ${toolName}`, {
      inputSize: JSON.stringify(input).length,
      outputSize: JSON.stringify(output).length,
      duration: `${duration}ms`,
      success: !output.error
    });
  }

  // Utility methods
  createChildLogger(context) {
    return new ChildLogger(this, context);
  }

  setLevel(level) {
    if (level in this.#logLevels) {
      this.#config.set('logging.level', level);
    }
  }

  getLogFile() {
    return this.#config.getLoggingConfig().file;
  }

  clearLogs() {
    const logConfig = this.#config.getLoggingConfig();
    const logDir = dirname(logConfig.file);
    
    try {
      const logFiles = readdirSync(logDir)
        .filter(file => file.startsWith('arien-ai.log'));
      
      for (const file of logFiles) {
        unlinkSync(join(logDir, file));
      }
      
      this.info('Log files cleared');
    } catch (error) {
      this.error('Failed to clear logs', { error: error.message });
    }
  }
}

/**
 * Child Logger for contextual logging
 */
class ChildLogger {
  #parent;
  #context;

  constructor(parent, context) {
    this.#parent = parent;
    this.#context = context;
  }

  #addContext(meta) {
    return { ...this.#context, ...meta };
  }

  error(message, meta = {}) {
    this.#parent.error(message, this.#addContext(meta));
  }

  warn(message, meta = {}) {
    this.#parent.warn(message, this.#addContext(meta));
  }

  info(message, meta = {}) {
    this.#parent.info(message, this.#addContext(meta));
  }

  debug(message, meta = {}) {
    this.#parent.debug(message, this.#addContext(meta));
  }

  logCommand(command, result) {
    this.#parent.logCommand(command, result);
  }

  logLLMRequest(provider, model, prompt, response) {
    this.#parent.logLLMRequest(provider, model, prompt, response);
  }

  logError(error, context = {}) {
    this.#parent.logError(error, this.#addContext(context));
  }

  logPerformance(operation, duration, meta = {}) {
    this.#parent.logPerformance(operation, duration, this.#addContext(meta));
  }

  logRetry(attempt, maxAttempts, error, delay) {
    this.#parent.logRetry(attempt, maxAttempts, error, delay);
  }

  logToolExecution(toolName, input, output, duration) {
    this.#parent.logToolExecution(toolName, input, output, duration);
  }
}
