/**
 * Basic functionality tests
 */

import { test, describe } from 'node:test';
import assert from 'node:assert';
import { Config } from '../src/utils/config.js';
import { Logger } from '../src/utils/logger.js';
import { Validator } from '../src/utils/validator.js';

describe('Basic Functionality Tests', () => {
  test('Config initialization', async () => {
    const config = new Config();
    await config.load();
    
    assert.ok(config.get('version'));
    assert.ok(config.get('llm.providers.deepseek'));
    assert.ok(config.get('tools.shell'));
  });

  test('Logger creation', () => {
    const config = new Config();
    const logger = new Logger(config);
    
    assert.ok(logger);
    // Logger should not throw when logging
    logger.info('Test message');
    logger.debug('Debug message');
  });

  test('Validator functions', () => {
    // URL validation
    assert.ok(Validator.validateUrl('https://example.com').valid);
    assert.ok(!Validator.validateUrl('invalid-url').valid);
    
    // Command validation
    assert.ok(Validator.validateCommand('ls -la').valid);
    assert.ok(!Validator.validateCommand('').valid);
    
    // JSON validation
    assert.ok(Validator.validateJSON('{"test": true}').valid);
    assert.ok(!Validator.validateJSON('invalid json').valid);
    
    // Input sanitization
    assert.strictEqual(Validator.sanitizeInput('  test  '), 'test');
    assert.strictEqual(Validator.sanitizeInput('test\x00'), 'test');
  });

  test('Package.json structure', async () => {
    const { readFileSync } = await import('fs');
    const { join } = await import('path');
    
    const packageJson = JSON.parse(readFileSync(join(process.cwd(), 'package.json'), 'utf8'));
    
    assert.ok(packageJson.name);
    assert.ok(packageJson.version);
    assert.ok(packageJson.main);
    assert.ok(packageJson.bin);
    assert.ok(packageJson.dependencies);
  });
});

// Run a simple integration test
describe('Integration Tests', () => {
  test('Module imports work correctly', async () => {
    // Test that all main modules can be imported
    const modules = [
      '../src/utils/config.js',
      '../src/utils/logger.js',
      '../src/utils/validator.js',
      '../src/cli/progress-indicator.js',
      '../src/tools/shell-tool.js',
      '../src/tools/web-tool.js'
    ];

    for (const modulePath of modules) {
      try {
        await import(modulePath);
      } catch (error) {
        assert.fail(`Failed to import ${modulePath}: ${error.message}`);
      }
    }
  });
});
