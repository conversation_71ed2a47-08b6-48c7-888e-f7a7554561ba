{"version": "1.0.0", "llm": {"providers": {"deepseek": {"enabled": true, "apiKey": "", "baseUrl": "https://api.deepseek.com", "models": {"chat": "deepseek-chat", "reasoner": "deepseek-reasoner"}, "maxTokens": 4096, "temperature": 0.7, "timeout": 30000}, "ollama": {"enabled": true, "baseUrl": "http://localhost:11434", "models": {"default": "llama3.2:latest"}, "timeout": 60000}}, "defaultProvider": "deepseek", "fallbackProvider": "ollama"}, "tools": {"shell": {"enabled": true, "timeout": 30000, "maxOutputSize": 1048576, "allowedCommands": [], "blockedCommands": ["rm -rf /", "format c:", "del /f /s /q", "sudo rm -rf", "chmod 777 /"], "requireConfirmation": ["rm", "del", "format", "sudo", "chmod 777"]}, "web": {"enabled": true, "timeout": 15000, "maxResponseSize": 5242880, "userAgent": "Arien-AI/1.0.0", "followRedirects": true, "maxRedirects": 5}}, "ui": {"theme": "default", "animations": true, "progressIndicator": "ball", "colors": true, "timestamps": true, "verbose": false}, "logging": {"level": "info", "file": "~/.arien-ai/logs/arien-ai.log", "maxSize": 10485760, "maxFiles": 5, "console": true}, "retry": {"maxAttempts": 3, "baseDelay": 1000, "maxDelay": 30000, "exponentialBase": 2, "jitter": true}, "security": {"confirmDestructiveActions": true, "sandboxMode": false, "allowNetworkAccess": true, "allowFileSystemAccess": true}}