import os from 'node:os';

const nameMap = new Map([
	[24, ['Sequoia', '15']],
	[23, ['Sonoma', '14']],
	[22, ['<PERSON>', '13']],
	[21, ['<PERSON>', '12']],
	[20, ['Big Sur', '11']],
	[19, ['<PERSON>', '10.15']],
	[18, ['Mojave', '10.14']],
	[17, ['High Sierra', '10.13']],
	[16, ['Sierra', '10.12']],
	[15, ['El Capitan', '10.11']],
	[14, ['Yosemite', '10.10']],
	[13, ['Mavericks', '10.9']],
	[12, ['Mountain Lion', '10.8']],
	[11, ['Lion', '10.7']],
	[10, ['<PERSON> Leopard', '10.6']],
	[9, ['<PERSON><PERSON>', '10.5']],
	[8, ['<PERSON>', '10.4']],
	[7, ['<PERSON>', '10.3']],
	[6, ['<PERSON>', '10.2']],
	[5, ['Puma', '10.1']],
]);

export default function macosRelease(release) {
	release = Number((release || os.release()).split('.')[0]);

	const [name, version] = nameMap.get(release) || ['Unknown', ''];

	return {
		name,
		version,
	};
}
