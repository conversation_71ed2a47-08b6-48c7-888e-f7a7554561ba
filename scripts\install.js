#!/usr/bin/env node

/**
 * Arien-AI Global Installation Script
 * Cross-platform installer for Windows 11 WSL, macOS, and Linux
 */

import { execSync, spawn } from 'child_process';
import { existsSync, writeFileSync, readFileSync, mkdirSync, chmodSync, unlinkSync, rmSync } from 'fs';
import { join, dirname } from 'path';
import { homedir, platform, arch } from 'os';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class ArienAIInstaller {
  constructor() {
    this.platform = platform();
    this.arch = arch();
    this.isWSL = this.#detectWSL();
    this.packageRoot = join(__dirname, '..');
    this.installDir = this.#getInstallDirectory();
    this.binDir = this.#getBinDirectory();
    this.configDir = join(homedir(), '.arien-ai');
  }

  async install() {
    console.log('🚀 Installing Arien-AI...\n');
    
    try {
      // Check prerequisites
      await this.#checkPrerequisites();
      
      // Create directories
      this.#createDirectories();
      
      // Install dependencies
      await this.#installDependencies();
      
      // Create global command
      this.#createGlobalCommand();
      
      // Initialize configuration
      await this.#initializeConfig();
      
      // Add to PATH if needed
      this.#updatePath();
      
      console.log('✅ Arien-AI installed successfully!\n');
      this.#showPostInstallInstructions();
      
    } catch (error) {
      console.error('❌ Installation failed:', error.message);
      process.exit(1);
    }
  }

  async update() {
    console.log('🔄 Updating Arien-AI...\n');
    
    try {
      // Check if already installed
      if (!this.#isInstalled()) {
        throw new Error('Arien-AI is not installed. Run with --install first.');
      }
      
      // Update dependencies
      await this.#installDependencies();
      
      // Update global command
      this.#createGlobalCommand();
      
      console.log('✅ Arien-AI updated successfully!\n');
      
    } catch (error) {
      console.error('❌ Update failed:', error.message);
      process.exit(1);
    }
  }

  async uninstall() {
    console.log('🗑️  Uninstalling Arien-AI...\n');
    
    try {
      // Remove global command
      this.#removeGlobalCommand();
      
      // Remove from PATH
      this.#removeFromPath();
      
      // Optionally remove config
      const removeConfig = process.argv.includes('--remove-config');
      if (removeConfig) {
        this.#removeConfig();
      }
      
      console.log('✅ Arien-AI uninstalled successfully!\n');
      
      if (!removeConfig) {
        console.log('💡 Configuration files preserved. Use --remove-config to remove them.');
      }
      
    } catch (error) {
      console.error('❌ Uninstall failed:', error.message);
      process.exit(1);
    }
  }

  // Private methods

  #detectWSL() {
    try {
      const release = readFileSync('/proc/version', 'utf8');
      return release.toLowerCase().includes('microsoft');
    } catch {
      return false;
    }
  }

  #getInstallDirectory() {
    switch (this.platform) {
      case 'win32':
        return join(process.env.APPDATA || join(homedir(), 'AppData', 'Roaming'), 'arien-ai');
      case 'darwin':
        return join(homedir(), '.local', 'share', 'arien-ai');
      case 'linux':
        return join(homedir(), '.local', 'share', 'arien-ai');
      default:
        throw new Error(`Unsupported platform: ${this.platform}`);
    }
  }

  #getBinDirectory() {
    switch (this.platform) {
      case 'win32':
        return join(process.env.APPDATA || join(homedir(), 'AppData', 'Roaming'), 'arien-ai', 'bin');
      case 'darwin':
        return join(homedir(), '.local', 'bin');
      case 'linux':
        return join(homedir(), '.local', 'bin');
      default:
        throw new Error(`Unsupported platform: ${this.platform}`);
    }
  }

  async #checkPrerequisites() {
    console.log('🔍 Checking prerequisites...');
    
    // Check Node.js version
    try {
      const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
      const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
      
      if (majorVersion < 20) {
        throw new Error(`Node.js 20+ required, found ${nodeVersion}`);
      }
      
      console.log(`✅ Node.js ${nodeVersion}`);
    } catch (error) {
      throw new Error('Node.js 20+ is required but not found');
    }
    
    // Check npm
    try {
      const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
      console.log(`✅ npm ${npmVersion}`);
    } catch (error) {
      throw new Error('npm is required but not found');
    }
    
    // Check git (optional)
    try {
      const gitVersion = execSync('git --version', { encoding: 'utf8' }).trim();
      console.log(`✅ ${gitVersion}`);
    } catch (error) {
      console.log('⚠️  Git not found (optional)');
    }
  }

  #createDirectories() {
    console.log('📁 Creating directories...');
    
    const dirs = [this.installDir, this.binDir, this.configDir];
    
    for (const dir of dirs) {
      if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
        console.log(`✅ Created ${dir}`);
      }
    }
  }

  async #installDependencies() {
    console.log('📦 Installing dependencies...');
    
    try {
      // Copy package files to install directory
      const packageJson = readFileSync(join(this.packageRoot, 'package.json'), 'utf8');
      writeFileSync(join(this.installDir, 'package.json'), packageJson);
      
      // Install dependencies
      execSync('npm install --production', {
        cwd: this.installDir,
        stdio: 'inherit'
      });
      
      // Copy source files
      this.#copySourceFiles();
      
      console.log('✅ Dependencies installed');
    } catch (error) {
      throw new Error(`Failed to install dependencies: ${error.message}`);
    }
  }

  #copySourceFiles() {
    // Copy src directory
    if (this.platform === 'win32') {
      execSync(`xcopy "${join(this.packageRoot, 'src')}" "${join(this.installDir, 'src')}" /E /I /Y`, { stdio: 'inherit' });
      execSync(`xcopy "${join(this.packageRoot, 'config')}" "${join(this.installDir, 'config')}" /E /I /Y`, { stdio: 'inherit' });
    } else {
      execSync(`cp -r "${join(this.packageRoot, 'src')}" "${this.installDir}/"`, { stdio: 'inherit' });
      execSync(`cp -r "${join(this.packageRoot, 'config')}" "${this.installDir}/"`, { stdio: 'inherit' });
    }
  }

  #createGlobalCommand() {
    console.log('🔗 Creating global command...');
    
    const mainScript = join(this.installDir, 'src', 'index.js');
    
    if (this.platform === 'win32') {
      // Create batch file for Windows
      const batchContent = `@echo off
node "${mainScript}" %*`;
      
      const batchFile = join(this.binDir, 'arien.bat');
      writeFileSync(batchFile, batchContent);
      
      // Create PowerShell script
      const psContent = `#!/usr/bin/env pwsh
node "${mainScript}" $args`;
      
      const psFile = join(this.binDir, 'arien.ps1');
      writeFileSync(psFile, psContent);
      
      console.log(`✅ Created ${batchFile}`);
      console.log(`✅ Created ${psFile}`);
    } else {
      // Create shell script for Unix-like systems
      const shellContent = `#!/usr/bin/env bash
exec node "${mainScript}" "$@"`;
      
      const shellFile = join(this.binDir, 'arien');
      writeFileSync(shellFile, shellContent);
      chmodSync(shellFile, 0o755);
      
      console.log(`✅ Created ${shellFile}`);
    }
  }

  async #initializeConfig() {
    console.log('⚙️  Initializing configuration...');
    
    const configFile = join(this.configDir, 'config.json');
    
    if (!existsSync(configFile)) {
      // Create default config
      const defaultConfig = {
        version: '1.0.0',
        initialized: new Date().toISOString(),
        platform: this.platform,
        installDir: this.installDir
      };
      
      writeFileSync(configFile, JSON.stringify(defaultConfig, null, 2));
      console.log(`✅ Created ${configFile}`);
    }
  }

  #updatePath() {
    console.log('🛤️  Updating PATH...');
    
    if (this.platform === 'win32') {
      console.log('💡 Add the following to your PATH environment variable:');
      console.log(`   ${this.binDir}`);
    } else {
      const shellRc = this.#getShellRcFile();
      const pathExport = `export PATH="$PATH:${this.binDir}"`;
      
      try {
        const rcContent = existsSync(shellRc) ? readFileSync(shellRc, 'utf8') : '';
        
        if (!rcContent.includes(this.binDir)) {
          writeFileSync(shellRc, `${rcContent}\n# Arien-AI\n${pathExport}\n`, { flag: 'a' });
          console.log(`✅ Added to ${shellRc}`);
        } else {
          console.log(`✅ Already in ${shellRc}`);
        }
      } catch (error) {
        console.log('💡 Manually add the following to your shell profile:');
        console.log(`   ${pathExport}`);
      }
    }
  }

  #getShellRcFile() {
    const shell = process.env.SHELL || '/bin/bash';
    
    if (shell.includes('zsh')) {
      return join(homedir(), '.zshrc');
    } else if (shell.includes('fish')) {
      return join(homedir(), '.config', 'fish', 'config.fish');
    } else {
      return join(homedir(), '.bashrc');
    }
  }

  #isInstalled() {
    const mainScript = join(this.installDir, 'src', 'index.js');
    return existsSync(mainScript);
  }

  #removeGlobalCommand() {
    const commands = this.platform === 'win32'
      ? [join(this.binDir, 'arien.bat'), join(this.binDir, 'arien.ps1')]
      : [join(this.binDir, 'arien')];

    for (const cmd of commands) {
      if (existsSync(cmd)) {
        unlinkSync(cmd);
        console.log(`✅ Removed ${cmd}`);
      }
    }
  }

  #removeFromPath() {
    // This would require more complex shell profile parsing
    console.log('💡 Manually remove the following from your PATH:');
    console.log(`   ${this.binDir}`);
  }

  #removeConfig() {
    if (existsSync(this.configDir)) {
      rmSync(this.configDir, { recursive: true, force: true });
      console.log(`✅ Removed ${this.configDir}`);
    }
  }

  #showPostInstallInstructions() {
    console.log('📋 Post-installation instructions:\n');
    
    if (this.platform === 'win32') {
      console.log('1. Add the following to your PATH environment variable:');
      console.log(`   ${this.binDir}\n`);
      console.log('2. Restart your terminal or command prompt\n');
    } else {
      console.log('1. Restart your terminal or run:');
      console.log(`   source ${this.#getShellRcFile()}\n`);
    }
    
    console.log('3. Initialize configuration:');
    console.log('   arien config --init\n');
    
    console.log('4. Start using Arien-AI:');
    console.log('   arien interactive\n');
    
    console.log('For help, run: arien help');
  }
}

// Main execution
async function main() {
  const installer = new ArienAIInstaller();
  
  const args = process.argv.slice(2);
  
  if (args.includes('--install') || args.length === 0) {
    await installer.install();
  } else if (args.includes('--update')) {
    await installer.update();
  } else if (args.includes('--uninstall')) {
    await installer.uninstall();
  } else {
    console.log('Usage: node install.js [--install|--update|--uninstall] [--remove-config]');
    process.exit(1);
  }
}

main().catch(error => {
  console.error('❌ Installation script failed:', error.message);
  process.exit(1);
});
