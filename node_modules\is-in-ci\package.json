{"name": "is-in-ci", "version": "1.0.0", "description": "Check if the process is running in a Continuous Integration (CI) environment", "license": "MIT", "repository": "sindresorhus/is-in-ci", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "bin": "./cli.js", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && CI=1 ava && tsc index.d.ts"}, "files": ["index.js", "index.d.ts", "cli.js"], "keywords": ["ci", "continuous", "integration", "environment", "server", "check", "detect", "determine", "test", "cli", "cli-app"], "devDependencies": {"ava": "^5.3.1", "typescript": "^5.2.2", "xo": "^0.56.0"}}