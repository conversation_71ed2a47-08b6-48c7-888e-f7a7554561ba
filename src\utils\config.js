/**
 * Configuration Management System
 * Handles loading, saving, and managing application configuration
 */

import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { homedir } from 'os';
import { fileURLToPath } from 'url';
import chalk from 'chalk';
import inquirer from 'inquirer';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

export class Config {
  #configPath;
  #config;
  #defaultConfig;

  constructor() {
    // Configuration file location
    this.#configPath = join(homedir(), '.arien-ai', 'config.json');
    
    // Default configuration
    this.#defaultConfig = {
      version: '1.0.0',
      llm: {
        providers: {
          deepseek: {
            enabled: true,
            apiKey: '',
            baseUrl: 'https://api.deepseek.com/v1',
            models: {
              chat: 'deepseek-chat',
              reasoner: 'deepseek-reasoner'
            },
            maxTokens: 4096,
            temperature: 0.7,
            timeout: 30000
          },
          ollama: {
            enabled: true,
            baseUrl: 'http://localhost:11434',
            models: {
              default: 'llama3.2:latest'
            },
            timeout: 60000
          }
        },
        defaultProvider: 'deepseek',
        fallbackProvider: 'ollama'
      },
      tools: {
        shell: {
          enabled: true,
          timeout: 30000,
          maxOutputSize: 1024 * 1024, // 1MB
          allowedCommands: [],
          blockedCommands: ['rm -rf', 'format', 'del /f /s /q'],
          requireConfirmation: ['rm', 'del', 'format', 'sudo', 'chmod 777']
        },
        web: {
          enabled: true,
          timeout: 15000,
          maxResponseSize: 5 * 1024 * 1024, // 5MB
          userAgent: 'Arien-AI/1.0.0',
          followRedirects: true,
          maxRedirects: 5
        }
      },
      ui: {
        theme: 'default',
        animations: true,
        progressIndicator: 'ball',
        colors: true,
        timestamps: true,
        verbose: false
      },
      logging: {
        level: 'info',
        file: join(homedir(), '.arien-ai', 'logs', 'arien-ai.log'),
        maxSize: 10 * 1024 * 1024, // 10MB
        maxFiles: 5,
        console: true
      },
      retry: {
        maxAttempts: 3,
        baseDelay: 1000,
        maxDelay: 30000,
        exponentialBase: 2,
        jitter: true
      },
      security: {
        confirmDestructiveActions: true,
        sandboxMode: false,
        allowNetworkAccess: true,
        allowFileSystemAccess: true
      }
    };
  }

  async load() {
    try {
      if (existsSync(this.#configPath)) {
        const configData = readFileSync(this.#configPath, 'utf8');
        this.#config = { ...this.#defaultConfig, ...JSON.parse(configData) };
      } else {
        this.#config = { ...this.#defaultConfig };
        await this.save();
      }
    } catch (error) {
      console.warn(chalk.yellow('⚠️  Failed to load config, using defaults:', error.message));
      this.#config = { ...this.#defaultConfig };
    }
  }

  async save() {
    try {
      const configDir = dirname(this.#configPath);
      if (!existsSync(configDir)) {
        mkdirSync(configDir, { recursive: true });
      }
      
      writeFileSync(this.#configPath, JSON.stringify(this.#config, null, 2));
    } catch (error) {
      throw new Error(`Failed to save configuration: ${error.message}`);
    }
  }

  async initialize() {
    console.log(chalk.blue.bold('\n🔧 Initializing Arien-AI Configuration\n'));
    
    const questions = [
      {
        type: 'input',
        name: 'deepseekApiKey',
        message: 'Enter your Deepseek API key (optional):',
        validate: (input) => {
          if (!input) return true;
          return input.length > 10 || 'API key seems too short';
        }
      },
      {
        type: 'input',
        name: 'ollamaUrl',
        message: 'Ollama base URL:',
        default: 'http://localhost:11434'
      },
      {
        type: 'list',
        name: 'defaultProvider',
        message: 'Default LLM provider:',
        choices: ['deepseek', 'ollama'],
        default: 'deepseek'
      },
      {
        type: 'list',
        name: 'logLevel',
        message: 'Logging level:',
        choices: ['error', 'warn', 'info', 'debug'],
        default: 'info'
      },
      {
        type: 'confirm',
        name: 'confirmDestructive',
        message: 'Require confirmation for destructive actions?',
        default: true
      },
      {
        type: 'confirm',
        name: 'animations',
        message: 'Enable UI animations?',
        default: true
      }
    ];

    const answers = await inquirer.prompt(questions);

    // Update configuration with user answers
    if (answers.deepseekApiKey) {
      this.#config.llm.providers.deepseek.apiKey = answers.deepseekApiKey;
    }
    
    this.#config.llm.providers.ollama.baseUrl = answers.ollamaUrl;
    this.#config.llm.defaultProvider = answers.defaultProvider;
    this.#config.logging.level = answers.logLevel;
    this.#config.security.confirmDestructiveActions = answers.confirmDestructive;
    this.#config.ui.animations = answers.animations;

    await this.save();
    
    console.log(chalk.green('\n✅ Configuration saved successfully!'));
    console.log(chalk.gray(`Config file: ${this.#configPath}`));
  }

  async reset() {
    this.#config = { ...this.#defaultConfig };
    await this.save();
  }

  async show() {
    console.log(chalk.blue.bold('\n📋 Current Configuration\n'));
    console.log(chalk.gray(`Config file: ${this.#configPath}\n`));
    
    // Mask sensitive information
    const displayConfig = JSON.parse(JSON.stringify(this.#config));
    if (displayConfig.llm.providers.deepseek.apiKey) {
      displayConfig.llm.providers.deepseek.apiKey = '***masked***';
    }
    
    console.log(JSON.stringify(displayConfig, null, 2));
  }

  get(path) {
    return this.#getNestedValue(this.#config || this.#defaultConfig, path);
  }

  set(path, value) {
    if (!this.#config) {
      this.#config = { ...this.#defaultConfig };
    }
    this.#setNestedValue(this.#config, path, value);
  }

  getAll() {
    return { ...(this.#config || this.#defaultConfig) };
  }

  getConfigPath() {
    return this.#configPath;
  }

  // LLM Provider shortcuts
  getLLMConfig(provider = null) {
    const config = this.#config || this.#defaultConfig;
    const targetProvider = provider || config.llm.defaultProvider;
    return config.llm.providers[targetProvider];
  }

  getToolConfig(toolName) {
    const config = this.#config || this.#defaultConfig;
    return config.tools[toolName];
  }

  getUIConfig() {
    const config = this.#config || this.#defaultConfig;
    return config.ui;
  }

  getLoggingConfig() {
    return this.#config?.logging || this.#defaultConfig.logging;
  }

  getRetryConfig() {
    const config = this.#config || this.#defaultConfig;
    return config.retry;
  }

  getSecurityConfig() {
    const config = this.#config || this.#defaultConfig;
    return config.security;
  }

  // Helper methods
  #getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  #setNestedValue(obj, path, value) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    const target = keys.reduce((current, key) => {
      if (!(key in current)) current[key] = {};
      return current[key];
    }, obj);
    target[lastKey] = value;
  }

  // Validation methods
  isProviderEnabled(provider) {
    const config = this.#config || this.#defaultConfig;
    return config.llm.providers[provider]?.enabled ?? false;
  }

  isToolEnabled(tool) {
    const config = this.#config || this.#defaultConfig;
    return config.tools[tool]?.enabled ?? false;
  }

  shouldConfirmAction(command) {
    const config = this.#config || this.#defaultConfig;
    const requireConfirmation = config.tools.shell.requireConfirmation || [];
    return requireConfirmation.some(pattern => command.includes(pattern));
  }

  isCommandBlocked(command) {
    const config = this.#config || this.#defaultConfig;
    const blockedCommands = config.tools.shell.blockedCommands || [];
    return blockedCommands.some(pattern => command.includes(pattern));
  }
}
